# AI Tool Integration Module

This module implements OpenAI's function calling feature for eBay-Shopify Integration App, specifically focused on category suggestions when uploading Shopify products to eBay.

## Features

-   **OpenAI Function Calling**: Uses OpenAI's completion API with function calling capabilities
-   **eBay Browse API Integration**: Integrates with eBay's Browse API for category suggestions
-   **Product Title Analysis**: Analyzes Shopify product titles to suggest appropriate eBay categories
-   **AI-Powered Category Matching**: Uses AI to match product information with eBay categories

## Structure

```
AIToolIntegration/
├── Controllers/
│   └── CategorySuggestionController.php
├── Services/
│   ├── EbayBrowseApiService.php
│   └── CategorySuggestionService.php
├── Functions/
│   └── EbayBrowseFunctions.php
├── DTO/
│   ├── CategorySuggestionRequest.php
│   └── CategorySuggestionResponse.php
└── README.md
```

## Usage

1. User selects a Shopify product to upload to eBay
2. User requests AI Assistant for eBay category suggestions
3. AI Assistant analyzes the product title using OpenAI
4. AI Assistant calls eBay Browse API functions to get category suggestions
5. AI Assistant returns suggested categories and related item details

## API Endpoints

### 1. Get Category Suggestions for Shopify Product

```
POST /api/ai-tools/category-suggestions
```

**Request Body:**

```json
{
    "shopify_product_id": 123,
    "marketplace": "EBAY_US",
    "max_suggestions": 5
}
```

**Response:**

```json
{
    "success": true,
    "data": {
        "suggestions": [
            {
                "category_id": "9355",
                "category_name": "Cell Phones & Smartphones",
                "category_path": "Electronics > Cell Phones & Accessories > Cell Phones & Smartphones",
                "confidence": 0.95,
                "reasoning": "Product is clearly a smartphone based on title analysis"
            }
        ],
        "ai_analysis": "Based on the product title 'Apple iPhone 14 Pro Max', this is clearly a smartphone...",
        "usage": {
            "prompt_tokens": 150,
            "completion_tokens": 200,
            "total_tokens": 350
        },
        "product": {
            "id": 123,
            "title": "Apple iPhone 14 Pro Max 128GB Space Black",
            "marketplace": "EBAY_US"
        }
    }
}
```

### 2. Get Category Suggestions by Title (Testing)

```
POST /api/ai-tools/category-suggestions/by-title
```

**Request Body:**

```json
{
    "product_title": "Apple iPhone 14 Pro Max 128GB Space Black",
    "marketplace": "EBAY_US",
    "max_suggestions": 5
}
```

## Configuration

The module uses existing OpenAI configuration from `config/ai_assistant.php` and eBay API credentials from the main application.

### Required Environment Variables

```env
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4
EBAY_CLIENT_ID=your_ebay_client_id
EBAY_CLIENT_SECRET=your_ebay_client_secret
```

## Installation

1. The module files are already created in the correct Laravel structure
2. The service provider is registered in `config/app.php`
3. Routes are added to `routes/api.php`
4. Ensure OpenAI and eBay API credentials are configured

## Testing

Run the test script to verify the integration:

```bash
php AIToolIntegration/test_integration.php
```

## How It Works

### 1. OpenAI Function Calling Flow

1. User requests category suggestions for a Shopify product
2. System creates OpenAI messages with system prompt and user query
3. OpenAI analyzes the product title and decides which functions to call
4. System executes the requested eBay Browse API functions
5. Function results are sent back to OpenAI for final analysis
6. OpenAI provides structured category suggestions with reasoning

### 2. eBay Browse API Integration

-   **search_ebay_items**: Searches eBay for similar products to understand market categories
-   **get_category_suggestions**: Analyzes search results to extract category patterns
-   **analyze_category_fit**: Evaluates how well a product fits specific categories
-   **get_category_details**: Retrieves detailed information about eBay categories

### 3. AI Assistant Capabilities

-   Analyzes product titles using advanced NLP
-   Understands product context and market positioning
-   Provides confidence scores for each category suggestion
-   Explains reasoning behind each recommendation
-   Considers marketplace-specific category structures

## Integration with Existing System

This module integrates seamlessly with your existing:

-   **OpenAI Service**: Uses the existing `AIAssistant\Backend\Services\LLM\OpenAIService`
-   **eBay API Infrastructure**: Leverages existing `RestfulService` and authentication
-   **Laravel Architecture**: Follows existing patterns for controllers, services, and DTOs
-   **AI Assistant Configuration**: Uses existing `config/ai_assistant.php` settings

## Future Enhancements

1. **Enhanced Product Analysis**: Include product descriptions and images
2. **Category Validation**: Verify suggested categories against eBay's current taxonomy
3. **Performance Optimization**: Cache frequently requested category suggestions
4. **Multi-language Support**: Support for different eBay marketplaces and languages
5. **Advanced Analytics**: Track suggestion accuracy and user feedback
