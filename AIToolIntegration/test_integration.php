<?php

/**
 * Test script for AI Tool Integration
 * 
 * This script demonstrates how to use the OpenAI function calling feature
 * for eBay category suggestions.
 */

require_once __DIR__ . '/../web/vendor/autoload.php';

use App\Http\Controllers\AIToolIntegration\DTO\CategorySuggestionRequest;
use App\Http\Controllers\AIToolIntegration\Services\CategorySuggestionService;
use App\Http\Controllers\AIToolIntegration\Services\EbayBrowseApiService;
use App\Http\Controllers\AIToolIntegration\Functions\EbayBrowseFunctions;

echo "=== AI Tool Integration Test ===\n\n";

// Test 1: Function Definitions
echo "1. Testing Function Definitions:\n";
$functions = EbayBrowseFunctions::getFunctionDefinitions();
echo "Number of functions defined: " . count($functions) . "\n";
foreach ($functions as $function) {
    echo "- " . $function['name'] . ": " . $function['description'] . "\n";
}
echo "\n";

// Test 2: System Prompt
echo "2. Testing System Prompt:\n";
$systemPrompt = EbayBrowseFunctions::getSystemPrompt();
echo "System prompt length: " . strlen($systemPrompt) . " characters\n";
echo "Preview: " . substr($systemPrompt, 0, 100) . "...\n\n";

// Test 3: User Prompt Template
echo "3. Testing User Prompt Template:\n";
$testTitle = "Apple iPhone 14 Pro Max 128GB Space Black Unlocked";
$userPrompt = EbayBrowseFunctions::getUserPromptTemplate($testTitle);
echo "User prompt for test product:\n";
echo $userPrompt . "\n\n";

// Test 4: DTO Classes
echo "4. Testing DTO Classes:\n";
$request = new CategorySuggestionRequest(
    shopifyProductId: 123,
    productTitle: $testTitle,
    productDescription: "Latest iPhone with advanced camera system",
    productTags: ['electronics', 'smartphone', 'apple'],
    marketplace: 'EBAY_US',
    maxSuggestions: 5
);

echo "Request DTO created successfully\n";
echo "Product Title: " . $request->productTitle . "\n";
echo "Marketplace: " . $request->marketplace . "\n";
echo "Max Suggestions: " . $request->maxSuggestions . "\n\n";

echo "=== Test Complete ===\n";
echo "All components are properly structured and ready for integration.\n";
echo "\nNext steps:\n";
echo "1. Set up OpenAI API key in your .env file\n";
echo "2. Configure eBay API credentials\n";
echo "3. Test the API endpoints with a real request\n";
echo "4. Integrate with your frontend application\n";
