<?php

declare(strict_types=1);

namespace App\Http\Controllers\AIToolIntegration\DTO;

class CategorySuggestionRequest
{
    public function __construct(
        public readonly int $shopifyProductId,
        public readonly string $productTitle,
        public readonly ?string $productDescription = null,
        public readonly ?array $productTags = null,
        public readonly ?string $marketplace = 'EBAY_US',
        public readonly int $maxSuggestions = 5
    ) {
    }

    public function toArray(): array
    {
        return [
            'shopify_product_id' => $this->shopifyProductId,
            'product_title' => $this->productTitle,
            'product_description' => $this->productDescription,
            'product_tags' => $this->productTags,
            'marketplace' => $this->marketplace,
            'max_suggestions' => $this->maxSuggestions,
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            shopifyProductId: $data['shopify_product_id'],
            productTitle: $data['product_title'],
            productDescription: $data['product_description'] ?? null,
            productTags: $data['product_tags'] ?? null,
            marketplace: $data['marketplace'] ?? 'EBAY_US',
            maxSuggestions: $data['max_suggestions'] ?? 5
        );
    }
}
