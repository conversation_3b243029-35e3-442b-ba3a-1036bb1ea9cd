<?php

declare(strict_types=1);

namespace App\Http\Controllers\AIToolIntegration\Services;

use App\Models\Ebay\EbayUser;
use App\Module\Ebay\Helper\Helper;
use App\Module\Ebay\Services\API\RestfulService;
use Exception;
use Illuminate\Support\Facades\Log;

class EbayBrowseApiService
{
    private const BROWSE_API_BASE_URL = 'https://api.ebay.com/buy/browse/v1';
    private const BROWSE_API_SANDBOX_URL = 'https://api.sandbox.ebay.com/buy/browse/v1';

    public function __construct(
        private readonly EbayUser $ebayUser
    ) {
    }

    /**
     * Search for items on eBay to get category suggestions
     */
    public function searchItems(string $query, string $marketplace = 'EBAY_US', int $limit = 10): array
    {
        try {
            $service = new RestfulService(Helper::getEbayAccessToken($this->ebayUser));
            
            $baseUrl = config('ebay.sandbox') ? self::BROWSE_API_SANDBOX_URL : self::BROWSE_API_BASE_URL;
            $uri = $baseUrl . '/item_summary/search';
            
            $params = [
                'q' => $query,
                'limit' => $limit,
                'category_ids' => '', // Let eBay suggest categories
                'sort' => 'price', // Sort by price for better relevance
            ];

            $response = $service->performAPICallWithParams($uri, $params);
            
            if (isset($response->errors)) {
                Log::error('eBay Browse API search error', [
                    'query' => $query,
                    'errors' => $response->errors
                ]);
                return [];
            }

            return $this->processSearchResponse($response);
            
        } catch (Exception $e) {
            Log::error('eBay Browse API search exception', [
                'query' => $query,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get category suggestions based on a search query
     */
    public function getCategorySuggestions(string $query, string $marketplace = 'EBAY_US'): array
    {
        $searchResults = $this->searchItems($query, $marketplace, 20);
        
        if (empty($searchResults)) {
            return [];
        }

        // Extract categories from search results
        $categories = [];
        foreach ($searchResults as $item) {
            if (isset($item['categories'])) {
                foreach ($item['categories'] as $category) {
                    $categoryId = $category['categoryId'] ?? '';
                    if (!isset($categories[$categoryId])) {
                        $categories[$categoryId] = [
                            'category_id' => $categoryId,
                            'category_name' => $category['categoryName'] ?? '',
                            'category_path' => $this->buildCategoryPath($category),
                            'count' => 0,
                            'sample_items' => []
                        ];
                    }
                    $categories[$categoryId]['count']++;
                    
                    // Add sample items (max 3 per category)
                    if (count($categories[$categoryId]['sample_items']) < 3) {
                        $categories[$categoryId]['sample_items'][] = [
                            'title' => $item['title'] ?? '',
                            'price' => $item['price']['value'] ?? 0,
                            'currency' => $item['price']['currency'] ?? 'USD'
                        ];
                    }
                }
            }
        }

        // Sort categories by frequency and return top suggestions
        uasort($categories, fn($a, $b) => $b['count'] <=> $a['count']);
        
        return array_slice(array_values($categories), 0, 10);
    }

    /**
     * Get detailed category information
     */
    public function getCategoryDetails(string $categoryId): ?array
    {
        try {
            // Use existing category service for detailed category info
            // This integrates with your existing EbayCategoriesService
            return [
                'category_id' => $categoryId,
                'details' => 'Category details would be fetched from existing category service'
            ];
        } catch (Exception $e) {
            Log::error('Error fetching category details', [
                'category_id' => $categoryId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    private function processSearchResponse($response): array
    {
        if (!isset($response->itemSummaries)) {
            return [];
        }

        $items = [];
        foreach ($response->itemSummaries as $item) {
            $items[] = [
                'item_id' => $item->itemId ?? '',
                'title' => $item->title ?? '',
                'price' => [
                    'value' => $item->price->value ?? 0,
                    'currency' => $item->price->currency ?? 'USD'
                ],
                'categories' => $this->extractCategories($item),
                'condition' => $item->condition ?? '',
                'seller' => [
                    'username' => $item->seller->username ?? '',
                    'feedback_percentage' => $item->seller->feedbackPercentage ?? 0
                ]
            ];
        }

        return $items;
    }

    private function extractCategories($item): array
    {
        $categories = [];
        
        if (isset($item->categories)) {
            foreach ($item->categories as $category) {
                $categories[] = [
                    'categoryId' => $category->categoryId ?? '',
                    'categoryName' => $category->categoryName ?? ''
                ];
            }
        }

        return $categories;
    }

    private function buildCategoryPath(array $category): string
    {
        // Build category path from category hierarchy
        // This would typically involve traversing the category tree
        return $category['categoryName'] ?? '';
    }
}
