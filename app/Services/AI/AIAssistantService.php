<?php

namespace App\Services\AI;

use App\Models\AI\AIConversation;
use App\Models\AI\AIMessage;
use App\Models\AI\AIKnowledgeChunk;
use App\Services\AI\LLM\OpenAIService;
use App\Services\AI\VectorStore\PineconeService;
use Illuminate\Support\Facades\Log;
use Exception;

class AIAssistantService
{
    private OpenAIService $openAIService;
    private PineconeService $pineconeService;

    public function __construct(OpenAIService $openAIService, PineconeService $pineconeService)
    {
        $this->openAIService = $openAIService;
        $this->pineconeService = $pineconeService;
    }

    /**
     * Process a user message and generate AI response.
     */
    public function processMessage(string $message, AIConversation $conversation): array
    {
        try {
            // Store user message
            $userMessage = AIMessage::create([
                'conversation_id' => $conversation->id,
                'role' => 'user',
                'content' => $message,
                'metadata' => [],
            ]);

            // Get conversation context
            $conversationHistory = $this->getConversationHistory($conversation);

            // Retrieve relevant knowledge
            $relevantKnowledge = $this->retrieveRelevantKnowledge($message);

            // Generate AI response
            $aiResponse = $this->generateResponse($message, $conversationHistory, $relevantKnowledge);

            if (!$aiResponse['success']) {
                throw new Exception('Failed to generate AI response: ' . $aiResponse['error']);
            }

            // Store AI message
            $aiMessage = AIMessage::create([
                'conversation_id' => $conversation->id,
                'role' => 'assistant',
                'content' => $aiResponse['content'],
                'metadata' => [
                    'sources' => $relevantKnowledge['sources'] ?? [],
                    'usage' => $aiResponse['usage'] ?? [],
                ],
            ]);

            // Update conversation
            $conversation->update([
                'last_activity_at' => now(),
            ]);

            return [
                'success' => true,
                'content' => $aiResponse['content'],
                'message_id' => $aiMessage->id,
                'metadata' => [
                    'sources' => $relevantKnowledge['sources'] ?? [],
                    'usage' => $aiResponse['usage'] ?? [],
                ],
            ];

        } catch (Exception $e) {
            Log::error('AI Assistant message processing failed', [
                'conversation_id' => $conversation->id,
                'message' => $message,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get conversation history for context.
     */
    private function getConversationHistory(AIConversation $conversation, int $limit = 10): array
    {
        return $conversation->messages()
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->reverse()
            ->map(function ($message) {
                return [
                    'role' => $message->role,
                    'content' => $message->content,
                ];
            })
            ->toArray();
    }

    /**
     * Retrieve relevant knowledge from vector store.
     */
    private function retrieveRelevantKnowledge(string $query): array
    {
        try {
            // Generate embedding for the query
            $embeddingResult = $this->openAIService->generateEmbedding($query);
            
            if (!$embeddingResult['success']) {
                Log::warning('Failed to generate embedding for knowledge 
                retrieval', [
                    'query' => $query,
                    'error' => $embeddingResult['error'],
                ]);
                return ['chunks' => [], 'sources' => []];
            }

            // Search vector store
            $searchResult = $this->pineconeService->query(
                $embeddingResult['embedding'],
                5, // top_k
                ['type' => 'knowledge_chunk']
            );

            if (!$searchResult['success']) {
                Log::warning('Failed to search vector store', [
                    'query' => $query,
                    'error' => $searchResult['error'],
                ]);
                return ['chunks' => [], 'sources' => []];
            }

            // Get knowledge chunks from database
            $chunkIds = collect($searchResult['matches'])->pluck('id')->toArray();
            $chunks = AIKnowledgeChunk::whereIn('id', $chunkIds)->get();

            // Format sources
            $sources = $chunks->map(function ($chunk) {
                return [
                    'title' => $chunk->document->title ?? 'Unknown Document',
                    'chunk_id' => $chunk->id,
                    'relevance_score' => 0.8, // You could get this from Pinecone results
                ];
            })->unique('title')->values()->toArray();

            return [
                'chunks' => $chunks->pluck('content')->toArray(),
                'sources' => $sources,
            ];

        } catch (Exception $e) {
            Log::error('Knowledge retrieval failed', [
                'query' => $query,
                'error' => $e->getMessage(),
            ]);

            return ['chunks' => [], 'sources' => []];
        }
    }

    /**
     * Generate AI response using OpenAI.
     */
    private function generateResponse(string $userMessage, array $conversationHistory, array $knowledgeContext): array
    {
        // Build system prompt
        $systemPrompt = $this->buildSystemPrompt($knowledgeContext['chunks']);

        // Build messages array
        $messages = [
            ['role' => 'system', 'content' => $systemPrompt],
        ];

        // Add conversation history (excluding system messages)
        foreach ($conversationHistory as $historyMessage) {
            if ($historyMessage['role'] !== 'system') {
                $messages[] = $historyMessage;
            }
        }

        // Add current user message
        $messages[] = ['role' => 'user', 'content' => $userMessage];

        // Generate response
        return $this->openAIService->chat($messages, [], [
            'max_tokens' => 1000,
            'temperature' => 0.7,
        ]);
    }

    /**
     * Build system prompt with knowledge context.
     */
    private function buildSystemPrompt(array $knowledgeChunks): string
    {
        $basePrompt = "You are an AI assistant specialized in eBay-Shopify integration. You help users with:

1. Profile configuration and category selection
2. Inventory synchronization troubleshooting  
3. Platform-specific guidance for eBay and Shopify
4. General integration support and best practices

Always provide helpful, accurate, and actionable advice. If you're not certain about something, acknowledge the limitation and suggest where the user might find more information.";

        if (!empty($knowledgeChunks)) {
            $contextText = implode("\n\n", $knowledgeChunks);
            $basePrompt .= "\n\nRelevant knowledge context:\n" . $contextText;
        }

        return $basePrompt;
    }
}
