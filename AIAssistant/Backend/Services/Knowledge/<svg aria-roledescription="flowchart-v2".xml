<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2105.03125 1642" style="max-width: 2105.03125px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019"><style>#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .error-icon{fill:#a44141;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .edge-thickness-normal{stroke-width:1px;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .marker.cross{stroke:lightgrey;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 p{margin:0;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .cluster-label text{fill:#F9FFFE;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .cluster-label span{color:#F9FFFE;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .cluster-label span p{background-color:transparent;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .label text,#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 span{fill:#ccc;color:#ccc;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .node rect,#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .node circle,#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .node ellipse,#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .node polygon,#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .rough-node .label text,#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .node .label text,#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .image-shape .label,#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .icon-shape .label{text-anchor:middle;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .rough-node .label,#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .node .label,#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .image-shape .label,#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .icon-shape .label{text-align:center;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .node.clickable{cursor:pointer;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .arrowheadPath{fill:lightgrey;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .cluster text{fill:#F9FFFE;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .cluster span{color:#F9FFFE;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 rect.text{fill:none;stroke-width:0;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .icon-shape,#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .icon-shape p,#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .icon-shape rect,#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph5" class="cluster"><rect height="104" width="901.078125" y="1530" x="1170.65625" style=""></rect><g transform="translate(1565.57421875, 1530)" class="cluster-label"><foreignObject height="24" width="111.2421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>App Integration</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph4" class="cluster"><rect height="104" width="1156.5625" y="395" x="8" style=""></rect><g transform="translate(518.15625, 395)" class="cluster-label"><foreignObject height="24" width="136.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Knowledge Sources</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="416" width="271.8359375" y="935" x="691.4296875" style=""></rect><g transform="translate(771.58203125, 935)" class="cluster-label"><foreignObject height="24" width="111.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>LLM Integration</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="336" width="1068.4609375" y="549" x="55.720703125" style=""></rect><g transform="translate(544.193359375, 549)" class="cluster-label"><foreignObject height="24" width="91.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>RAG Pipeline</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="1343" width="405.966796875" y="137" x="1231.611328125" style=""></rect><g transform="translate(1376.0595703125, 137)" class="cluster-label"><foreignObject height="24" width="117.0703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Laravel Backend</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="337" width="439.453125" y="8" x="1657.578125" style=""></rect><g transform="translate(1777.3046875, 8)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Frontend (React + Shopify Polaris)</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UI_API_0" d="M1774.246,87L1774.246,91.167C1774.246,95.333,1774.246,103.667,1774.246,112C1774.246,120.333,1774.246,128.667,1734.571,139.163C1694.897,149.66,1615.547,162.32,1575.873,168.65L1536.198,174.98"></path><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QA_API_1" d="M1983.973,87L1983.973,91.167C1983.973,95.333,1983.973,103.667,1983.973,112C1983.973,120.333,1983.973,128.667,1909.349,140.078C1834.725,151.489,1685.477,165.978,1610.853,173.222L1536.229,180.466"></path><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API_QC_2" d="M1437.942,216L1436.339,220.167C1434.736,224.333,1431.531,232.667,1429.929,240.333C1428.326,248,1428.326,255,1428.326,258.5L1428.326,262"></path><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_QC_CM_3" d="M1428.326,320L1428.326,324.167C1428.326,328.333,1428.326,336.667,1428.326,345C1428.326,353.333,1428.326,361.667,1428.326,370C1428.326,378.333,1428.326,386.667,1428.326,394.333C1428.326,402,1428.326,409,1428.326,412.5L1428.326,416"></path><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CM_RE_4" d="M1428.326,474L1428.326,478.167C1428.326,482.333,1428.326,490.667,1428.326,499C1428.326,507.333,1428.326,515.667,1428.326,524C1428.326,532.333,1428.326,540.667,1372.464,551.631C1316.602,562.595,1204.877,576.19,1149.015,582.987L1093.152,589.785"></path><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RE_VS_5" d="M1000.982,628L1000.982,632.167C1000.982,636.333,1000.982,644.667,985.048,654.706C969.114,664.746,937.246,676.493,921.312,682.366L905.378,688.239"></path><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_VS_CB_6" d="M827.348,756L827.348,760.167C827.348,764.333,827.348,772.667,827.348,780.333C827.348,788,827.348,795,827.348,798.5L827.348,802"></path><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CB_PB_7" d="M827.348,860L827.348,864.167C827.348,868.333,827.348,876.667,827.348,885C827.348,893.333,827.348,901.667,827.348,910C827.348,918.333,827.348,926.667,827.348,934.333C827.348,942,827.348,949,827.348,952.5L827.348,956"></path><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PB_LLM_8" d="M827.348,1014L827.348,1018.167C827.348,1022.333,827.348,1030.667,827.348,1038.333C827.348,1046,827.348,1053,827.348,1056.5L827.348,1060"></path><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LLM_RP_9" d="M827.348,1118L827.348,1122.167C827.348,1126.333,827.348,1134.667,827.348,1142.333C827.348,1150,827.348,1157,827.348,1160.5L827.348,1164"></path><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RP_FC_10" d="M827.348,1222L827.348,1226.167C827.348,1230.333,827.348,1238.667,827.348,1246.333C827.348,1254,827.348,1261,827.348,1264.5L827.348,1268"></path><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FC_TE_11" d="M827.348,1326L827.348,1330.167C827.348,1334.333,827.348,1342.667,930.924,1351C1034.5,1359.333,1241.652,1367.667,1345.229,1375.333C1448.805,1383,1448.805,1390,1448.805,1393.5L1448.805,1397"></path><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TE_PA_12" d="M1369.707,1451.327L1353.503,1456.106C1337.298,1460.885,1304.889,1470.442,1288.685,1479.388C1272.48,1488.333,1272.48,1496.667,1272.48,1505C1272.48,1513.333,1272.48,1521.667,1272.48,1529.333C1272.48,1537,1272.48,1544,1272.48,1547.5L1272.48,1551"></path><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TE_SA_13" d="M1448.805,1455L1448.805,1459.167C1448.805,1463.333,1448.805,1471.667,1448.805,1480C1448.805,1488.333,1448.805,1496.667,1448.805,1505C1448.805,1513.333,1448.805,1521.667,1448.805,1529.333C1448.805,1537,1448.805,1544,1448.805,1547.5L1448.805,1551"></path><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TE_UA_14" d="M1527.902,1452.37L1542.848,1456.975C1557.794,1461.58,1587.686,1470.79,1602.632,1479.562C1617.578,1488.333,1617.578,1496.667,1617.578,1505C1617.578,1513.333,1617.578,1521.667,1617.578,1529.333C1617.578,1537,1617.578,1544,1617.578,1547.5L1617.578,1551"></path><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AD_DP_15" d="M143.441,474L143.441,478.167C143.441,482.333,143.441,490.667,143.441,499C143.441,507.333,143.441,515.667,143.441,524C143.441,532.333,143.441,540.667,206.184,551.594C268.926,562.521,394.41,576.041,457.152,582.802L519.894,589.562"></path><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SD_DP_16" d="M390.977,474L390.977,478.167C390.977,482.333,390.977,490.667,390.977,499C390.977,507.333,390.977,515.667,390.977,524C390.977,532.333,390.977,540.667,412.475,549.589C433.973,558.511,476.969,568.022,498.467,572.778L519.966,577.533"></path><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ED_DP_17" d="M626.051,474L626.051,478.167C626.051,482.333,626.051,490.667,626.051,499C626.051,507.333,626.051,515.667,626.051,524C626.051,532.333,626.051,540.667,626.051,548.333C626.051,556,626.051,563,626.051,566.5L626.051,570"></path><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ST_DP_18" d="M849.367,474L849.367,478.167C849.367,482.333,849.367,490.667,849.367,499C849.367,507.333,849.367,515.667,849.367,524C849.367,532.333,849.367,540.667,829.827,549.383C810.287,558.1,771.207,567.2,751.666,571.75L732.126,576.3"></path><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UG_DP_19" d="M1057.133,474L1057.133,478.167C1057.133,482.333,1057.133,490.667,1057.133,499C1057.133,507.333,1057.133,515.667,1057.133,524C1057.133,532.333,1057.133,540.667,1002.978,551.366C948.822,562.065,840.512,575.13,786.357,581.663L732.202,588.195"></path><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DP_VS_20" d="M626.051,628L626.051,632.167C626.051,636.333,626.051,644.667,646.585,655.362C667.12,666.057,708.189,679.115,728.724,685.644L749.258,692.172"></path><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API_CP_21" d="M1448.326,216L1448.326,220.167C1448.326,224.333,1448.326,232.667,1488.84,243.276C1529.354,253.886,1610.381,266.771,1650.895,273.214L1691.409,279.657"></path><path marker-end="url(#mermaid-f635be8d-ee3c-4b87-96ff-b3ca059ea019_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_API_TP_22" d="M1479.2,216L1483.965,220.167C1488.729,224.333,1498.258,232.667,1568.608,244.021C1638.957,255.376,1770.127,269.751,1835.712,276.939L1901.297,284.127"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(1774.24609375, 60)" id="flowchart-UI-0" class="node default"><rect height="54" width="163.3359375" y="-27" x="-81.66796875" style="" class="basic label-container"></rect><g transform="translate(-51.66796875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="103.3359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Chat Interface</p></span></div></foreignObject></g></g><g transform="translate(1983.97265625, 60)" id="flowchart-QA-1" class="node default"><rect height="54" width="156.1171875" y="-27" x="-78.05859375" style="" class="basic label-container"></rect><g transform="translate(-48.05859375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96.1171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Quick Actions</p></span></div></foreignObject></g></g><g transform="translate(1775.31640625, 293)" id="flowchart-CP-2" class="node default"><rect height="54" width="159.9140625" y="-27" x="-79.95703125" style="" class="basic label-container"></rect><g transform="translate(-49.95703125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="99.9140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Context Panel</p></span></div></foreignObject></g></g><g transform="translate(1982.26171875, 293)" id="flowchart-TP-3" class="node default"><rect height="54" width="153.9765625" y="-27" x="-76.98828125" style="" class="basic label-container"></rect><g transform="translate(-46.98828125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="93.9765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Task Progress</p></span></div></foreignObject></g></g><g transform="translate(1448.326171875, 189)" id="flowchart-API-4" class="node default"><rect height="54" width="167.84375" y="-27" x="-83.921875" style="" class="basic label-container"></rect><g transform="translate(-53.921875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="107.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>AI Assistant API</p></span></div></foreignObject></g></g><g transform="translate(1428.326171875, 293)" id="flowchart-QC-5" class="node default"><rect height="54" width="172.84375" y="-27" x="-86.421875" style="" class="basic label-container"></rect><g transform="translate(-56.421875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Query Classifier</p></span></div></foreignObject></g></g><g transform="translate(1428.326171875, 447)" id="flowchart-CM-6" class="node default"><rect height="54" width="181.03125" y="-27" x="-90.515625" style="" class="basic label-container"></rect><g transform="translate(-60.515625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="121.03125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Context Manager</p></span></div></foreignObject></g></g><g transform="translate(1448.8046875, 1428)" id="flowchart-TE-7" class="node default"><rect height="54" width="158.1953125" y="-27" x="-79.09765625" style="" class="basic label-container"></rect><g transform="translate(-49.09765625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="98.1953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Task Executor</p></span></div></foreignObject></g></g><g transform="translate(626.05078125, 601)" id="flowchart-DP-8" class="node default"><rect height="54" width="204.359375" y="-27" x="-102.1796875" style="" class="basic label-container"></rect><g transform="translate(-72.1796875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="144.359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Document Processor</p></span></div></foreignObject></g></g><g transform="translate(827.34765625, 717)" id="flowchart-VS-9" class="node default"><rect height="78" width="148.5546875" y="-39" x="-74.27734375" style="" class="basic label-container"></rect><g transform="translate(-44.27734375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="88.5546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Vector Store<br>Pinecone</p></span></div></foreignObject></g></g><g transform="translate(1000.982421875, 601)" id="flowchart-RE-10" class="node default"><rect height="54" width="176.3984375" y="-27" x="-88.19921875" style="" class="basic label-container"></rect><g transform="translate(-58.19921875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="116.3984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Retrieval Engine</p></span></div></foreignObject></g></g><g transform="translate(827.34765625, 833)" id="flowchart-CB-11" class="node default"><rect height="54" width="172.0859375" y="-27" x="-86.04296875" style="" class="basic label-container"></rect><g transform="translate(-56.04296875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112.0859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Context Builder</p></span></div></foreignObject></g></g><g transform="translate(827.34765625, 1091)" id="flowchart-LLM-12" class="node default"><rect height="54" width="201.8359375" y="-27" x="-100.91796875" style="" class="basic label-container"></rect><g transform="translate(-70.91796875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="141.8359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>OpenAI GPT-4 Turbo</p></span></div></foreignObject></g></g><g transform="translate(827.34765625, 987)" id="flowchart-PB-13" class="node default"><rect height="54" width="167.2734375" y="-27" x="-83.63671875" style="" class="basic label-container"></rect><g transform="translate(-53.63671875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="107.2734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Prompt Builder</p></span></div></foreignObject></g></g><g transform="translate(827.34765625, 1195)" id="flowchart-RP-14" class="node default"><rect height="54" width="174.3515625" y="-27" x="-87.17578125" style="" class="basic label-container"></rect><g transform="translate(-57.17578125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="114.3515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Response Parser</p></span></div></foreignObject></g></g><g transform="translate(827.34765625, 1299)" id="flowchart-FC-15" class="node default"><rect height="54" width="175.609375" y="-27" x="-87.8046875" style="" class="basic label-container"></rect><g transform="translate(-57.8046875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="115.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Function Calling</p></span></div></foreignObject></g></g><g transform="translate(143.44140625, 447)" id="flowchart-AD-16" class="node default"><rect height="54" width="200.8828125" y="-27" x="-100.44140625" style="" class="basic label-container"></rect><g transform="translate(-70.44140625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="140.8828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>App Documentation</p></span></div></foreignObject></g></g><g transform="translate(390.9765625, 447)" id="flowchart-SD-17" class="node default"><rect height="54" width="194.1875" y="-27" x="-97.09375" style="" class="basic label-container"></rect><g transform="translate(-67.09375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="134.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Shopify Knowledge</p></span></div></foreignObject></g></g><g transform="translate(626.05078125, 447)" id="flowchart-ED-18" class="node default"><rect height="54" width="175.9609375" y="-27" x="-87.98046875" style="" class="basic label-container"></rect><g transform="translate(-57.98046875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="115.9609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>eBay Knowledge</p></span></div></foreignObject></g></g><g transform="translate(849.3671875, 447)" id="flowchart-ST-19" class="node default"><rect height="54" width="170.671875" y="-27" x="-85.3359375" style="" class="basic label-container"></rect><g transform="translate(-55.3359375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="110.671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Support Tickets</p></span></div></foreignObject></g></g><g transform="translate(1057.1328125, 447)" id="flowchart-UG-20" class="node default"><rect height="54" width="144.859375" y="-27" x="-72.4296875" style="" class="basic label-container"></rect><g transform="translate(-42.4296875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="84.859375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User Guides</p></span></div></foreignObject></g></g><g transform="translate(1272.48046875, 1582)" id="flowchart-PA-21" class="node default"><rect height="54" width="133.6484375" y="-27" x="-66.82421875" style="" class="basic label-container"></rect><g transform="translate(-36.82421875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="73.6484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Profile API</p></span></div></foreignObject></g></g><g transform="translate(1448.8046875, 1582)" id="flowchart-SA-22" class="node default"><rect height="54" width="119" y="-27" x="-59.5" style="" class="basic label-container"></rect><g transform="translate(-29.5, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="59"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Sync API</p></span></div></foreignObject></g></g><g transform="translate(1617.578125, 1582)" id="flowchart-UA-23" class="node default"><rect height="54" width="118.546875" y="-27" x="-59.2734375" style="" class="basic label-container"></rect><g transform="translate(-29.2734375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="58.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User API</p></span></div></foreignObject></g></g><g transform="translate(1787.265625, 1582)" id="flowchart-EA-24" class="node default"><rect height="54" width="120.828125" y="-27" x="-60.4140625" style="" class="basic label-container"></rect><g transform="translate(-30.4140625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="60.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>eBay API</p></span></div></foreignObject></g></g><g transform="translate(1967.20703125, 1582)" id="flowchart-SHA-25" class="node default"><rect height="54" width="139.0546875" y="-27" x="-69.52734375" style="" class="basic label-container"></rect><g transform="translate(-39.52734375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="79.0546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Shopify API</p></span></div></foreignObject></g></g></g></g></g></svg>