<?php

declare(strict_types=1);

namespace App\Http\Controllers\AIToolIntegration\Functions;

class EbayBrowseFunctions
{
    /**
     * Get OpenAI function definitions for eBay Browse API
     */
    public static function getFunctionDefinitions(): array
    {
        return [
            [
                'name' => 'search_ebay_items',
                'description' => 'Search for items on eBay to analyze categories and get product insights',
                'parameters' => [
                    'type' => 'object',
                    'properties' => [
                        'query' => [
                            'type' => 'string',
                            'description' => 'Search query based on product title or keywords'
                        ],
                        'marketplace' => [
                            'type' => 'string',
                            'description' => 'eBay marketplace (default: EBAY_US)',
                            'enum' => ['EBAY_US', 'EBAY_GB', 'EBAY_DE', 'EBAY_AU', 'EBAY_CA']
                        ],
                        'limit' => [
                            'type' => 'integer',
                            'description' => 'Maximum number of items to return (default: 10, max: 50)',
                            'minimum' => 1,
                            'maximum' => 50
                        ]
                    ],
                    'required' => ['query']
                ]
            ],
            [
                'name' => 'get_category_suggestions',
                'description' => 'Get eBay category suggestions based on product information',
                'parameters' => [
                    'type' => 'object',
                    'properties' => [
                        'product_title' => [
                            'type' => 'string',
                            'description' => 'The Shopify product title to analyze'
                        ],
                        'marketplace' => [
                            'type' => 'string',
                            'description' => 'eBay marketplace (default: EBAY_US)',
                            'enum' => ['EBAY_US', 'EBAY_GB', 'EBAY_DE', 'EBAY_AU', 'EBAY_CA']
                        ]
                    ],
                    'required' => ['product_title']
                ]
            ],
            [
                'name' => 'analyze_category_fit',
                'description' => 'Analyze how well a product fits into specific eBay categories',
                'parameters' => [
                    'type' => 'object',
                    'properties' => [
                        'product_title' => [
                            'type' => 'string',
                            'description' => 'The product title to analyze'
                        ],
                        'category_ids' => [
                            'type' => 'array',
                            'items' => [
                                'type' => 'string'
                            ],
                            'description' => 'Array of eBay category IDs to analyze'
                        ],
                        'marketplace' => [
                            'type' => 'string',
                            'description' => 'eBay marketplace (default: EBAY_US)'
                        ]
                    ],
                    'required' => ['product_title', 'category_ids']
                ]
            ],
            [
                'name' => 'get_category_details',
                'description' => 'Get detailed information about specific eBay categories',
                'parameters' => [
                    'type' => 'object',
                    'properties' => [
                        'category_id' => [
                            'type' => 'string',
                            'description' => 'The eBay category ID to get details for'
                        ]
                    ],
                    'required' => ['category_id']
                ]
            ]
        ];
    }

    /**
     * Get the system prompt for category suggestion AI
     */
    public static function getSystemPrompt(): string
    {
        return "You are an expert eBay category suggestion assistant. Your role is to help users find the most appropriate eBay categories for their Shopify products.

When analyzing a product title, you should:
1. Use the search_ebay_items function to find similar products on eBay
2. Use get_category_suggestions to get category recommendations
3. Analyze the results and provide confident, well-reasoned category suggestions
4. Consider factors like product type, brand, condition, and target audience
5. Provide multiple category options when appropriate, ranked by relevance

Always explain your reasoning for each category suggestion and highlight any important considerations for the user.

Focus on accuracy and relevance - it's better to suggest fewer, highly relevant categories than many loosely related ones.";
    }

    /**
     * Get user prompt template for category suggestions
     */
    public static function getUserPromptTemplate(string $productTitle): string
    {
        return "Please analyze this Shopify product title and suggest the most appropriate eBay categories for listing:

Product Title: \"{$productTitle}\"

Please:
1. Search for similar items on eBay to understand the market
2. Identify the most relevant eBay categories
3. Provide 3-5 category suggestions ranked by relevance
4. Explain your reasoning for each suggestion
5. Highlight any important considerations or alternatives

Focus on categories that will give the product the best visibility and reach the right buyers.";
    }
}
