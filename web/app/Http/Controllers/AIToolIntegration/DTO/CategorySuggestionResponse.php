<?php

declare(strict_types=1);

namespace App\Http\Controllers\AIToolIntegration\DTO;

class CategorySuggestionResponse
{
    public function __construct(
        public readonly bool $success,
        public readonly array $suggestions,
        public readonly ?string $aiAnalysis = null,
        public readonly ?string $error = null,
        public readonly ?array $usage = null
    ) {
    }

    public function toArray(): array
    {
        return [
            'success' => $this->success,
            'suggestions' => $this->suggestions,
            'ai_analysis' => $this->aiAnalysis,
            'error' => $this->error,
            'usage' => $this->usage,
        ];
    }

    public static function success(array $suggestions, ?string $aiAnalysis = null, ?array $usage = null): self
    {
        return new self(
            success: true,
            suggestions: $suggestions,
            aiAnalysis: $aiAnalysis,
            usage: $usage
        );
    }

    public static function error(string $error): self
    {
        return new self(
            success: false,
            suggestions: [],
            error: $error
        );
    }
}

class CategorySuggestion
{
    public function __construct(
        public readonly string $categoryId,
        public readonly string $categoryName,
        public readonly string $categoryPath,
        public readonly float $confidence,
        public readonly ?array $relatedItems = null,
        public readonly ?string $reasoning = null
    ) {
    }

    public function toArray(): array
    {
        return [
            'category_id' => $this->categoryId,
            'category_name' => $this->categoryName,
            'category_path' => $this->categoryPath,
            'confidence' => $this->confidence,
            'related_items' => $this->relatedItems,
            'reasoning' => $this->reasoning,
        ];
    }
}
