<?php

declare(strict_types=1);

namespace App\Providers;

use App\Http\Controllers\AIToolIntegration\Services\CategorySuggestionService;
use App\Services\AI\LLM\OpenAIService;
use Illuminate\Support\ServiceProvider;

class AIToolIntegrationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register CategorySuggestionService
        $this->app->bind(CategorySuggestionService::class, function ($app) {
            return new CategorySuggestionService(
                $app->make(OpenAIService::class)
            );
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
