<?php

use App\Http\Controllers\AccountController;
use App\Http\Controllers\ActivityController;
use App\Http\Controllers\AppRating\AppRatingController;
use App\Http\Controllers\ContactUs\ContactUsController;
use App\Http\Controllers\Ebay\AccountDeletionWebhook;
use App\Http\Controllers\Ebay\AI\TitleSuggestionController;
use App\Http\Controllers\AIToolIntegration\CategorySuggestionController;
use App\Http\Controllers\Ebay\EbayProductUploadController;
use App\Http\Controllers\Ebay\StoreCategory\EbayStoreCategoryController;
use App\Http\Controllers\Ebay\UploadErrorResolutionController;
use App\Http\Controllers\EbaySettingController;
use App\Http\Controllers\OAuth\EbayOAuthController;
use App\Http\Controllers\Product\EbayProductController;
use App\Http\Controllers\Shopify\BillingController;
use App\Http\Controllers\Shopify\ProgressController;
use App\Http\Controllers\Shopify\ShopifyCollectionController;
use App\Http\Controllers\Shopify\ShopifyProductController;
use App\Http\Controllers\Shopify\ShopifyStoreController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Shopify\ShopifyMetafieldController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware(['shopify.auth'])->group(function () {
    Route::prefix('shopify')->group(function () {
        Route::controller(ShopifyProductController::class)->group(function () {
            Route::get('products', 'getProducts');
        });
        Route::controller(ShopifyCollectionController::class)->group(function () {
            Route::get('collection', 'getCollection');
            Route::get('collection-filter-list', 'getCollectionFilterList');
            Route::get('collection-products/{collection_id}', 'getCollectionProducts');
        });
        Route::put('update-navigator', [ShopifyStoreController::class, 'updateNavigator']);
        Route::get('product-fetching-progress', [ProgressController::class, 'fetchingProgress']);
        Route::post('sync/store-properties', [ShopifyStoreController::class, 'syncStoreDetail']);

        Route::get('shopify-product-metafield-definitions', [ShopifyMetafieldController::class, 'getProductMetafieldDefinitions']);
    });

    Route::prefix('ebay')->group(function () {
        Route::controller(ShopifyProductController::class)->group(function () {
            Route::post('/product/create/{ebayProduct}', 'createProduct');
            Route::post('/product/bulk-create', 'bulkCreateProduct');
            Route::post('/product/bulk-delete', 'bulkDeleteProduct');
            Route::get('shopify-products-status', 'shopifyProductsStatus');
        });
        Route::controller(EbayProductController::class)->group(function () {
            Route::get('get-ebay-products-count', 'getEbayProductCounts');
            Route::get('getProdFetchingDetails', 'checkIfDataFetchingFromEbay');
            Route::get('listings', 'getListings');
            Route::get('listings/{id}/variations', 'getVariations');
            Route::get('linked-listings', 'getLinkedListings');
            Route::post('unlink-listing/{ebayProduct}', 'unlinkListing');
            Route::get('fetchEbayListings', 'fetchProducts');
            Route::post('end-listings', 'ebayEndListing');
        });
        Route::post('/product-upload', [EbayProductUploadController::class, 'upload']);
        Route::post(
            '/resolve-product-upload/{error_id}',
            [UploadErrorResolutionController::class, 'resolveProductUpload']
        );
        Route::controller(EbayOAuthController::class)->group(function () {
            Route::post('/change-store', 'changeEbayStore');
        });

        /* Store Categories*/
        Route::get('current-user/store-categories', [EbayStoreCategoryController::class, 'getStoreCategories']);
        Route::get('current-user/sync/ebay-store-categories', [
            EbayStoreCategoryController::class,
            'syncEbayStoreCategories'
        ]);
    });

    Route::get('account_info', [AccountController::class, 'accountInfo']);
    Route::post('/contact-us', [ContactUsController::class, 'sendMail']);

    Route::controller(EbaySettingController::class)->group(function () {
        Route::post('/create-settings', 'create');
        Route::post('/update-settings', 'update');
        Route::get('/currency/', 'currency');
        Route::post('/shared-sku-sync', 'updateSharedSkuSyncSetting');
    });
    Route::get('activity', [ActivityController::class, 'index']);
    Route::post('accept-pricing-plan', [BillingController::class, 'requestPayment'])->name('confirm-pricing');

    Route::get('rating/session', [AppRatingController::class, 'show']);
    Route::post('rating/session', [AppRatingController::class, 'save']);

    Route::get('ai-title-suggestions/shopify-products/{id}', [TitleSuggestionController::class, 'getTitleSuggestion']);

    // AI Tool Integration Routes
    Route::prefix('ai-tools')->group(function () {
        Route::post('category-suggestions', [CategorySuggestionController::class, 'getCategorySuggestions']);
        Route::post('category-suggestions/by-title', [CategorySuggestionController::class, 'getCategorySuggestionsByTitle']);
    });
});
Route::post('ebay/notification', [AccountDeletionWebhook::class, 'deleteAccount']);
Route::get('ebay/notification', [AccountDeletionWebhook::class, 'deleteAccount']);
